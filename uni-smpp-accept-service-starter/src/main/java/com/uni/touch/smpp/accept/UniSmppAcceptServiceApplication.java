package com.uni.touch.smpp.accept;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"com.uni.touch.smpp.accept", "com.uni.touch.user"})
@EnableAspectJAutoProxy
@EnableCaching
@EnableScheduling
public class UniSmppAcceptServiceApplication {

    public static void main(String[] args) {

        long startTime = System.nanoTime();
        SpringApplication.run(UniSmppAcceptServiceApplication.class, args);
        preload(startTime);
    }

    protected static void preload(long startTime) {
        long endTime = System.nanoTime();
        System.out.println("Service startup in " + ((endTime - startTime) / 1000000) + " ms");
    }

}
