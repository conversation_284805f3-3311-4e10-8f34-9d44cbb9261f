<?xml version="1.0" encoding="UTF-8"?>
<!--
  #%L
  ch-smpp
  %%
  Copyright (C) 2009 - 2012 Cloudhopper by Twitter
  %%
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at
  
       http://www.apache.org/licenses/LICENSE-2.0
  
  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  #L%
  -->

<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <target>System.err</target>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%-23d [%thread] %-5level %logger{32} - %m%n</Pattern>
        </layout>
    </appender>
    
    <logger name="com.cloudhopper" level="DEBUG"/>

    <root level="WARN">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
