package com.uni.touch.smpp.accept.service.server;

import com.cloudhopper.smpp.SmppServerConfiguration;
import com.cloudhopper.smpp.SmppServerHandler;
import com.cloudhopper.smpp.impl.DefaultSmppServer;
import com.uni.touch.smpp.accept.common.config.SmppServerConfig;
import com.uni.touch.smpp.accept.common.config.SmppNormalPortConfig;
import com.uni.touch.smpp.accept.service.entity.SmppErrorInfo;
import com.uni.touch.smpp.accept.service.utils.SmppExceptionUtils;
import com.uni.touch.smpp.accept.service.handler.UniSmppServerHandler;
import com.uni.touch.smpp.accept.service.manager.SmppThreadPoolManager;
import com.uni.touch.smpp.accept.service.debug.DebugSmppServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;

/**
 * SMPP服务器启动器
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
@Component
public class SmppServerStarter {

    @Resource
    private UniSmppServerHandler uniSmppServerHandler;

    @Autowired
    private SmppThreadPoolManager threadPoolManager;

    /**
     * SMPP服务器
     */
    private DefaultSmppServer smppServer;

    @PostConstruct
    public void start() {
        if (!SmppServerConfig.ENABLED) {
            log.info("SMPP服务未启用，跳过启动");
            return;
        }

        try {
            log.info("启动SMPP服务器...");

            // 启动普通端口服务器
            if (SmppNormalPortConfig.ENABLED) {
                smppServer = startServer();
                log.info("SMPP服务器启动成功 - 端口: {}", SmppNormalPortConfig.PORT);
            }

            log.info("SMPP服务器启动完成");

        } catch (Exception e) {
            SmppExceptionUtils.logException("SMPP服务器启动", e);
            // 清理已启动的服务器
            stop();
            throw new RuntimeException(SmppExceptionUtils.createSmppException(SmppErrorInfo.SMPP_SERVER_START_FAILED, e.getMessage()));
        }
    }

    @PreDestroy
    public void stop() {
        log.info("停止SMPP服务器...");

        try {
            if (smppServer != null) {
                smppServer.stop();
                log.info("SMPP服务器已停止");
            }

            log.info("SMPP服务器停止完成");

        } catch (Exception e) {
            SmppExceptionUtils.logException("SMPP服务器停止", e);
        }
    }

    /**
     * 启动SMPP服务器
     */
    private DefaultSmppServer startServer() throws Exception {
        SmppServerConfiguration configuration = createServerConfiguration();
        log.info("即将启动 SMPP 服务器");
        log.info("配置: host={}, port={}", configuration.getHost(), configuration.getPort());
        log.info("绑定超时时间: {}ms", configuration.getBindTimeout());
        log.info("Handler 实现类: {}", uniSmppServerHandler.getClass().getName());
        log.info("Handler 是否是 SmppServerHandler: {}", (uniSmppServerHandler instanceof SmppServerHandler));

        // 使用调试版本的服务器来获取更详细的日志
        DefaultSmppServer server = new DebugSmppServer(configuration, uniSmppServerHandler,
                threadPoolManager.getIoWorkerExecutor(), threadPoolManager.getMonitorExecutor());

        log.info("服务器创建完成，开始启动...");
        server.start();

        log.info("服务器启动后状态: started={}, stopped={}, destroyed={}",
                server.isStarted(), server.isStopped(), server.isDestroyed());
        log.info("服务器计数器: {}", server.getCounters());

        return server;
    }

    /**
     * 创建服务器配置
     */
    private SmppServerConfiguration createServerConfiguration() {
        SmppServerConfiguration configuration = new SmppServerConfiguration();

        // 基础网络配置
        configuration.setHost(SmppNormalPortConfig.HOST);
        configuration.setPort(SmppNormalPortConfig.PORT);
        configuration.setMaxConnectionSize(SmppNormalPortConfig.MAX_CONNECTIONS);

        // 服务器配置
        configuration.setName(SmppServerConfig.NAME);
        configuration.setSystemId(SmppServerConfig.SYSTEM_ID);
        configuration.setBindTimeout(SmppServerConfig.BIND_TIMEOUT);
        configuration.setConnectTimeout(SmppServerConfig.CONNECT_TIMEOUT);
        configuration.setNonBlockingSocketsEnabled(SmppServerConfig.NON_BLOCKING_SOCKETS_ENABLED);
        configuration.setReuseAddress(SmppServerConfig.REUSE_ADDRESS);
        configuration.setJmxEnabled(SmppServerConfig.JMX_ENABLED);
        configuration.setJmxDomain(SmppServerConfig.JMX_DOMAIN);
        configuration.setAutoNegotiateInterfaceVersion(SmppServerConfig.AUTO_NEGOTIATE_INTERFACE_VERSION);
        configuration.setInterfaceVersion(SmppServerConfig.INTERFACE_VERSION);

        // 窗口和超时配置
        configuration.setDefaultWindowSize(SmppServerConfig.DEFAULT_WINDOW_SIZE);
        configuration.setDefaultWindowWaitTimeout(SmppServerConfig.DEFAULT_WINDOW_WAIT_TIMEOUT);
        configuration.setDefaultRequestExpiryTimeout(SmppServerConfig.DEFAULT_REQUEST_EXPIRY_TIMEOUT);
        configuration.setDefaultWindowMonitorInterval(SmppServerConfig.DEFAULT_WINDOW_MONITOR_INTERVAL);
        configuration.setDefaultSessionCountersEnabled(SmppServerConfig.DEFAULT_SESSION_COUNTERS_ENABLED);

        return configuration;
    }

    /**
     * 获取服务器状态
     */
    public String getStatus() {
        StringBuilder status = new StringBuilder();
        status.append("SMPP服务器状态:\n");

        if (smppServer != null) {
            status.append("SMPP服务器: ").append(smppServer.isStarted() ? "运行中" : "已停止")
                    .append(" (端口: ").append(SmppNormalPortConfig.PORT).append(")\n");
        } else {
            status.append("SMPP服务器: 未启动\n");
        }

        return status.toString();
    }
}
