package com.uni.touch.smpp.accept.service.entity;

import com.cloudhopper.smpp.pdu.SubmitSm;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 分段消息组实体
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Data
public class SegmentMessageGroup {

    /**
     * 缓存键
     */
    private String cacheKey;

    /**
     * 系统ID
     */
    private String systemId;

    /**
     * 消息参考号
     */
    private int refNum;

    /**
     * 总分段数
     */
    private int totalSegments;

    /**
     * 分段类型
     */
    private SegmentType type;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 分段映射（分段号 -> SubmitSm）
     */
    private Map<Integer, SubmitSm> segments;

    /**
     * 构造方法
     */
    public SegmentMessageGroup(String cacheKey, String systemId, int refNum,
                               int totalSegments, SegmentType type) {
        this.cacheKey = cacheKey;
        this.systemId = systemId;
        this.refNum = refNum;
        this.totalSegments = totalSegments;
        this.type = type;
        this.createdTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
        this.segments = new ConcurrentHashMap<>();
    }

    /**
     * 添加分段
     */
    public boolean addSegment(int segmentNum, SubmitSm submitSm) {
        if (segmentNum < 1 || segmentNum > totalSegments) {
            return false;
        }

        segments.put(segmentNum, submitSm);
        lastUpdateTime = LocalDateTime.now();
        return true;
    }

    /**
     * 检查是否完整
     */
    public boolean isComplete() {
        return segments.size() == totalSegments;
    }

    /**
     * 获取第一个分段（用于获取基础信息）
     */
    public SubmitSm getFirstSegment() {
        return segments.get(1);
    }

    /**
     * 检查是否包含指定分段号
     */
    public boolean hasSegment(int segmentNum) {
        return segments.containsKey(segmentNum);
    }

    /**
     * 获取已收集的分段数量
     */
    public int getCollectedSegmentCount() {
        return segments.size();
    }
}
