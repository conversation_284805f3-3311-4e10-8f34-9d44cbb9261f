package com.uni.touch.smpp.accept.service.debug;

import com.cloudhopper.smpp.pdu.Pdu;
import lombok.extern.slf4j.Slf4j;
import org.jboss.netty.buffer.ChannelBuffer;
import org.jboss.netty.channel.*;

/**
 * SMPP调试处理器
 * 用于监控和记录所有通过Pipeline的事件和数据
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Slf4j
@ChannelPipelineCoverage("one")
public class SmppDebugHandler extends SimpleChannelHandler {

    private final String handlerName;

    public SmppDebugHandler(String handlerName) {
        this.handlerName = handlerName;
    }

    @Override
    public void channelConnected(ChannelHandlerContext ctx, ChannelStateEvent e) throws Exception {
        log.info("[{}] 客户端连接建立: {}", handlerName, e.getChannel().getRemoteAddress());
        super.channelConnected(ctx, e);
    }

    @Override
    public void channelDisconnected(ChannelHandlerContext ctx, ChannelStateEvent e) throws Exception {
        log.info("[{}] 客户端连接断开: {}", handlerName, e.getChannel().getRemoteAddress());
        super.channelDisconnected(ctx, e);
    }

    @Override
    public void messageReceived(ChannelHandlerContext ctx, MessageEvent e) throws Exception {
        Object message = e.getMessage();
        
        if (message instanceof ChannelBuffer) {
            ChannelBuffer buffer = (ChannelBuffer) message;
            log.info("[{}] 接收到原始数据: 长度={}, 可读字节={}", 
                    handlerName, buffer.capacity(), buffer.readableBytes());
            log.info("[{}] 原始数据内容: {}", handlerName, bufferToHexString(buffer));
        } else if (message instanceof Pdu) {
            Pdu pdu = (Pdu) message;
            log.info("[{}] 接收到解码后的PDU: 类型={}, 命令ID=0x{}, 序列号={}", 
                    handlerName, pdu.getClass().getSimpleName(), 
                    Integer.toHexString(pdu.getCommandId()), pdu.getSequenceNumber());
        } else {
            log.info("[{}] 接收到其他类型消息: {}", handlerName, message.getClass().getSimpleName());
        }
        
        super.messageReceived(ctx, e);
    }

    @Override
    public void writeRequested(ChannelHandlerContext ctx, MessageEvent e) throws Exception {
        Object message = e.getMessage();
        
        if (message instanceof ChannelBuffer) {
            ChannelBuffer buffer = (ChannelBuffer) message;
            log.info("[{}] 发送原始数据: 长度={}, 可读字节={}", 
                    handlerName, buffer.capacity(), buffer.readableBytes());
        } else if (message instanceof Pdu) {
            Pdu pdu = (Pdu) message;
            log.info("[{}] 发送PDU: 类型={}, 命令ID=0x{}, 序列号={}", 
                    handlerName, pdu.getClass().getSimpleName(), 
                    Integer.toHexString(pdu.getCommandId()), pdu.getSequenceNumber());
        }
        
        super.writeRequested(ctx, e);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, ExceptionEvent e) throws Exception {
        log.error("[{}] 捕获到异常: {}", handlerName, e.getCause().getMessage(), e.getCause());
        super.exceptionCaught(ctx, e);
    }

    /**
     * 将ChannelBuffer转换为十六进制字符串
     */
    private String bufferToHexString(ChannelBuffer buffer) {
        if (buffer == null || buffer.readableBytes() == 0) {
            return "空";
        }
        
        StringBuilder sb = new StringBuilder();
        int readerIndex = buffer.readerIndex();
        
        try {
            // 最多显示前64个字节
            int length = Math.min(buffer.readableBytes(), 64);
            for (int i = 0; i < length; i++) {
                if (i > 0) {
                    sb.append(" ");
                }
                sb.append(String.format("%02X", buffer.getByte(readerIndex + i)));
            }
            
            if (buffer.readableBytes() > 64) {
                sb.append(" ...(总共").append(buffer.readableBytes()).append("字节)");
            }
        } catch (Exception ex) {
            sb.append("解析错误: ").append(ex.getMessage());
        }
        
        return sb.toString();
    }
}
