package com.uni.touch.smpp.accept.service.processor;

import com.cloudhopper.smpp.pdu.SubmitSm;
import com.uni.touch.accept.api.request.UniSendSmsRequest;
import com.uni.touch.accept.api.response.UniSendSmsResponse;
import com.uni.touch.service.common.enums.MsgFrameTypeEnum;
import com.uni.touch.service.common.result.BaseResult;
import com.uni.touch.smpp.accept.integration.accept.UniSendSmsDubboService;
import com.uni.touch.smpp.accept.service.entity.SmppAccountInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SMPP短信提交业务服务
 * 处理短信提交的业务逻辑
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
@Service
public class SmppSubmitProcessor {

    @Autowired
    private UniSendSmsDubboService uniSendSmsDubboService;

    /**
     * 提交短信消息
     */
    public String submitMessage(SubmitSm submitSm, String message, SmppAccountInfo account) {
        log.info("开始提交短信 - appId: {}, systemId: {}, from: {}, to: {}, message: {}, length: {}",
                account.getAppId(), account.getSystemId(),
                submitSm.getSourceAddress().getAddress(),
                submitSm.getDestAddress().getAddress(),
                message,
                message.length());

        // 构造请求参数
        UniSendSmsRequest request = buildSendSmsRequest(submitSm, message, account);

        // 调用Dubbo接口
        BaseResult<UniSendSmsResponse> result = uniSendSmsDubboService.sendSms(request);

        if (result != null && result.isSuccess() && result.getData() != null) {
            // 短信提交成功，返回消息ID
            String messageId = result.getData().getBizId();
            Integer sendMsgNum = result.getData().getSendMsgNum();

            log.info("短信提交成功 - appId: {}, systemId: {}, messageId: {}, phoneNumber: {}, senderId: {}, sendMsgNum: {}",
                    account.getAppId(), account.getSystemId(), messageId,
                    submitSm.getDestAddress().getAddress(), submitSm.getSourceAddress().getAddress(), sendMsgNum);

            return messageId;
        } else {
            log.warn("短信提交失败 - appId: {}, systemId: {}, result: {}", account.getAppId(), account.getSystemId(), result);
            return null;
        }
    }

    /**
     * 构造发送短信请求
     */
    private UniSendSmsRequest buildSendSmsRequest(SubmitSm submitSm, String message, SmppAccountInfo account) {
        UniSendSmsRequest request = new UniSendSmsRequest();

        // 基础参数
        request.setAppId(account.getAppId());
        request.setPhoneNumber(submitSm.getDestAddress().getAddress());
        request.setSenderId(submitSm.getSourceAddress().getAddress());
        request.setMsgFrameType(MsgFrameTypeEnum.TEXT);
        request.setContent(message);

        log.info("构造发送短信请求 - phoneNumber: {}, senderId: {}, appId: {}, content: {}",
                request.getPhoneNumber(), request.getSenderId(), request.getAppId(), request.getContent());

        return request;
    }
}
