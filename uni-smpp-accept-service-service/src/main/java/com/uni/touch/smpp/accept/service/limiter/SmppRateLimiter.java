package com.uni.touch.smpp.accept.service.limiter;

import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SMPP流控管理器
 * 基于Google RateLimiter实现
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
@Component
public class SmppRateLimiter {

    /**
     * 提交速度限制器 - systemId -> RateLimiter
     */
    private final Map<String, RateLimiter> submitRateLimiters = new ConcurrentHashMap<>();

    /**
     * 状态报告速度限制器 - systemId -> RateLimiter
     */
    private final Map<String, RateLimiter> dlrRateLimiters = new ConcurrentHashMap<>();

    /**
     * 获取提交速度限制器（基于smppMaxSubmitSpeedPerLink）
     */
    public RateLimiter getSubmitRateLimiter(String systemId, Integer maxSubmitSpeed) {
        return submitRateLimiters.computeIfAbsent(systemId, k -> {
            double permitsPerSecond = maxSubmitSpeed != null && maxSubmitSpeed > 0 ? maxSubmitSpeed : 10.0;
            RateLimiter limiter = RateLimiter.create(permitsPerSecond);
            log.info("创建提交速度限制器 - systemId: {}, permitsPerSecond: {}", systemId, permitsPerSecond);
            return limiter;
        });
    }

    /**
     * 获取状态报告速度限制器（基于smppMaxDlrSpeedPerLink）
     */
    public RateLimiter getDlrRateLimiter(String systemId, Integer maxDlrSpeed) {
        return dlrRateLimiters.computeIfAbsent(systemId, k -> {
            double permitsPerSecond = maxDlrSpeed != null && maxDlrSpeed > 0 ? maxDlrSpeed : 10.0;
            RateLimiter limiter = RateLimiter.create(permitsPerSecond);
            log.info("创建状态报告速度限制器 - systemId: {}, permitsPerSecond: {}", systemId, permitsPerSecond);
            return limiter;
        });
    }

    /**
     * 更新提交速度限制
     */
    public void updateSubmitRateLimit(String systemId, Integer maxSubmitSpeed) {
        RateLimiter limiter = submitRateLimiters.get(systemId);
        if (limiter != null && maxSubmitSpeed != null && maxSubmitSpeed > 0) {
            limiter.setRate(maxSubmitSpeed);
            log.info("更新提交速度限制 - systemId: {}, newRate: {}", systemId, maxSubmitSpeed);
        }
    }

    /**
     * 更新状态报告速度限制
     */
    public void updateDlrRateLimit(String systemId, Integer maxDlrSpeed) {
        RateLimiter limiter = dlrRateLimiters.get(systemId);
        if (limiter != null && maxDlrSpeed != null && maxDlrSpeed > 0) {
            limiter.setRate(maxDlrSpeed);
            log.info("更新状态报告速度限制 - systemId: {}, newRate: {}", systemId, maxDlrSpeed);
        }
    }

    /**
     * 移除账号的所有限制器
     */
    public void removeRateLimiters(String systemId) {
        submitRateLimiters.remove(systemId);
        dlrRateLimiters.remove(systemId);
        log.info("移除速度限制器 - systemId: {}", systemId);
    }

    /**
     * 获取当前提交速度
     */
    public double getCurrentSubmitRate(String systemId) {
        RateLimiter limiter = submitRateLimiters.get(systemId);
        return limiter != null ? limiter.getRate() : 0.0;
    }

    /**
     * 获取当前状态报告速度
     */
    public double getCurrentDlrRate(String systemId) {
        RateLimiter limiter = dlrRateLimiters.get(systemId);
        return limiter != null ? limiter.getRate() : 0.0;
    }
}
