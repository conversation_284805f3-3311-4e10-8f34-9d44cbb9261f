package com.uni.touch.smpp.accept.service.utils;

import com.cloudhopper.smpp.pdu.SubmitSm;
import com.uni.touch.smpp.accept.service.entity.SegmentMessageGroup;
import com.uni.touch.smpp.accept.service.entity.SmppSarInfo;
import com.uni.touch.smpp.accept.service.entity.SmppUdhInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 分段消息验证服务
 * 专门负责分段消息的验证逻辑
 * 
 * <AUTHOR>
 * @date 2025/07/28
 */
@Slf4j
@Service
public class SegmentValidUtils {

    /**
     * 验证SAR分段信息的一致性
     * 
     * @param segmentMessageGroup 现有的分段组
     * @param sarInfo 新的SAR分段信息
     * @return 验证是否通过
     */
    public boolean validateSarConsistency(SegmentMessageGroup segmentMessageGroup, SmppSarInfo sarInfo) {
        if (segmentMessageGroup == null || sarInfo == null) {
            return false;
        }

        // 验证总分段数一致性
        if (segmentMessageGroup.getTotalSegments() != sarInfo.getTotalSegments()) {
            log.error("SAR分段总数不一致 - key: {}, 期望: {}, 实际: {}", 
                    segmentMessageGroup.getCacheKey(), segmentMessageGroup.getTotalSegments(), sarInfo.getTotalSegments());
            return false;
        }

        // 验证参考号一致性
        if (segmentMessageGroup.getRefNum() != sarInfo.getRefNum()) {
            log.error("SAR参考号不一致 - key: {}, 期望: {}, 实际: {}", 
                    segmentMessageGroup.getCacheKey(), segmentMessageGroup.getRefNum(), sarInfo.getRefNum());
            return false;
        }

        // 验证分段号范围
        if (sarInfo.getSegmentSeq() < 1 || sarInfo.getSegmentSeq() > sarInfo.getTotalSegments()) {
            log.error("SAR分段号超出范围 - key: {}, 分段号: {}, 总分段: {}", 
                    segmentMessageGroup.getCacheKey(), sarInfo.getSegmentSeq(), sarInfo.getTotalSegments());
            return false;
        }

        // 验证是否重复分段
        if (segmentMessageGroup.hasSegment(sarInfo.getSegmentSeq())) {
            log.warn("SAR分段重复 - key: {}, 分段号: {}", 
                    segmentMessageGroup.getCacheKey(), sarInfo.getSegmentSeq());
            return false;
        }

        return true;
    }

    /**
     * 验证UDH分段信息的一致性
     * 
     * @param segmentMessageGroup 现有的分段组
     * @param udhInfo 新的UDH分段信息
     * @return 验证是否通过
     */
    public boolean validateUdhConsistency(SegmentMessageGroup segmentMessageGroup, SmppUdhInfo udhInfo) {
        if (segmentMessageGroup == null || udhInfo == null) {
            return false;
        }

        // 验证总分段数一致性
        if (segmentMessageGroup.getTotalSegments() != udhInfo.getTotalParts()) {
            log.error("UDH分段总数不一致 - key: {}, 期望: {}, 实际: {}", 
                    segmentMessageGroup.getCacheKey(), segmentMessageGroup.getTotalSegments(), udhInfo.getTotalParts());
            return false;
        }

        // 验证参考号一致性
        if (segmentMessageGroup.getRefNum() != udhInfo.getRefNum()) {
            log.error("UDH参考号不一致 - key: {}, 期望: {}, 实际: {}", 
                    segmentMessageGroup.getCacheKey(), segmentMessageGroup.getRefNum(), udhInfo.getRefNum());
            return false;
        }

        // 验证分段号范围
        if (udhInfo.getPartNum() < 1 || udhInfo.getPartNum() > udhInfo.getTotalParts()) {
            log.error("UDH分段号超出范围 - key: {}, 分段号: {}, 总分段: {}", 
                    segmentMessageGroup.getCacheKey(), udhInfo.getPartNum(), udhInfo.getTotalParts());
            return false;
        }

        // 验证是否重复分段
        if (segmentMessageGroup.hasSegment(udhInfo.getPartNum())) {
            log.warn("UDH分段重复 - key: {}, 分段号: {}", 
                    segmentMessageGroup.getCacheKey(), udhInfo.getPartNum());
            return false;
        }

        return true;
    }

    /**
     * 验证分段消息的基本信息
     * 
     * @param submitSm 分段消息
     * @param systemId 系统ID
     * @return 验证是否通过
     */
    public boolean validateSegmentMessage(SubmitSm submitSm, String systemId) {
        if (submitSm == null) {
            log.error("分段消息为空");
            return false;
        }

        if (systemId == null || systemId.trim().isEmpty()) {
            log.error("系统ID为空");
            return false;
        }

        // 验证消息内容
        byte[] messageBytes = submitSm.getShortMessage();
        if (messageBytes == null) {
            log.warn("分段消息内容为空 - systemId: {}", systemId);
            // 允许空内容，某些分段可能确实为空
        }

        return true;
    }

    /**
     * 验证分段组的完整性和一致性
     * 
     * @param segmentMessageGroup 分段组
     * @return 验证是否通过
     */
    public boolean validateSegmentGroupIntegrity(SegmentMessageGroup segmentMessageGroup) {
        if (segmentMessageGroup == null) {
            log.error("分段组为空");
            return false;
        }

        // 验证基本信息
        if (segmentMessageGroup.getTotalSegments() <= 0) {
            log.error("分段组总分段数无效 - key: {}, totalSegments: {}", 
                    segmentMessageGroup.getCacheKey(), segmentMessageGroup.getTotalSegments());
            return false;
        }

        if (segmentMessageGroup.getSegments() == null) {
            log.error("分段组segments为空 - key: {}", segmentMessageGroup.getCacheKey());
            return false;
        }

        // 验证分段数量
        int collectedCount = segmentMessageGroup.getCollectedSegmentCount();
        if (collectedCount > segmentMessageGroup.getTotalSegments()) {
            log.error("分段组收集的分段数超过总数 - key: {}, collected: {}, total: {}", 
                    segmentMessageGroup.getCacheKey(), collectedCount, segmentMessageGroup.getTotalSegments());
            return false;
        }

        // 如果完整，验证所有分段是否存在
        if (segmentMessageGroup.isComplete()) {
            for (int i = 1; i <= segmentMessageGroup.getTotalSegments(); i++) {
                if (!segmentMessageGroup.hasSegment(i)) {
                    log.error("分段组声称完整但缺失分段 - key: {}, missing: {}", 
                            segmentMessageGroup.getCacheKey(), i);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 验证分段参数的合理性
     * 
     * @param segmentNum 分段号
     * @param totalSegments 总分段数
     * @param refNum 参考号
     * @return 验证是否通过
     */
    public boolean validateSegmentParameters(int segmentNum, int totalSegments, int refNum) {
        // 验证总分段数
        if (totalSegments <= 0 || totalSegments > 255) {
            log.error("总分段数超出合理范围 - totalSegments: {}", totalSegments);
            return false;
        }

        // 验证分段号
        if (segmentNum < 1 || segmentNum > totalSegments) {
            log.error("分段号超出范围 - segmentNum: {}, totalSegments: {}", segmentNum, totalSegments);
            return false;
        }

        // 验证参考号（通常是16位无符号整数）
        if (refNum < 0 || refNum > 65535) {
            log.error("参考号超出范围 - refNum: {}", refNum);
            return false;
        }

        return true;
    }
}
