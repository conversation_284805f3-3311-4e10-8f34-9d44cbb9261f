package com.uni.touch.smpp.accept.service.processor;

import com.cloudhopper.smpp.SmppConstants;
import com.cloudhopper.smpp.pdu.SubmitSm;
import com.uni.touch.smpp.accept.service.converter.SmppEncodingConverter;
import com.uni.touch.smpp.accept.service.entity.SegmentReassembleInfo;
import com.uni.touch.smpp.accept.service.entity.SegmentMessageGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 分段消息重组服务
 * 专门负责分段消息的重组逻辑
 * 
 * <AUTHOR>
 * @date 2025/07/28
 */
@Slf4j
@Service
public class SmppSegmentProcessor {

    @Autowired
    private SmppEncodingConverter encodingConverter;

    /**
     * 重组分段消息（使用统一的服务端编码）
     *
     * @param segmentMessageGroup 分段组
     * @param serverEncoding 服务端编码配置
     * @return 重组后的完整消息，如果重组失败返回null
     */
    public SegmentReassembleInfo reassemble(SegmentMessageGroup segmentMessageGroup, Integer serverEncoding) {
        if (segmentMessageGroup == null || !segmentMessageGroup.isComplete()) {
            log.warn("分段组不完整，无法重组 - key: {}, 已收集: {}/{}",
                    segmentMessageGroup != null ? segmentMessageGroup.getCacheKey() : "null",
                    segmentMessageGroup != null ? segmentMessageGroup.getCollectedSegmentCount() : 0,
                    segmentMessageGroup != null ? segmentMessageGroup.getTotalSegments() : 0);
            return null;
        }

        try {
            log.info("=== 开始分段消息重组 ===");
            log.info("重组信息 - key: {}, 类型: {}, 总分段: {}, 服务端编码: {}",
                    segmentMessageGroup.getCacheKey(), segmentMessageGroup.getType(),
                    segmentMessageGroup.getTotalSegments(), serverEncoding);

            StringBuilder completeMessage = new StringBuilder();
            SubmitSm firstSegment = segmentMessageGroup.getFirstSegment();

            if (firstSegment == null) {
                log.error("缺失第一个分段 - key: {}", segmentMessageGroup.getCacheKey());
                return null;
            }

            // 按序号重组消息
            for (int i = 1; i <= segmentMessageGroup.getTotalSegments(); i++) {
                SubmitSm segment = segmentMessageGroup.getSegments().get(i);
                if (segment == null) {
                    log.error("缺失分段 {} - key: {}", i, segmentMessageGroup.getCacheKey());
                    return null;
                }

                // 解码并拼接消息内容
                String segmentText = decodeSegmentMessage(segment, serverEncoding, i);
                log.info("分段 {} 解码完成 - 内容: '{}'", i, segmentText);
                if (segmentText != null) {
                    completeMessage.append(segmentText);
                }
            }

            String finalMessage = completeMessage.toString();
            log.info("分段消息重组成功 - 完整消息长度: {}, 完整内容: '{}'",
                    finalMessage.length(), finalMessage);
            log.info("=== 分段消息重组结束 ===");

            return new SegmentReassembleInfo(firstSegment, finalMessage,
                                        segmentMessageGroup.getRefNum(), segmentMessageGroup.getTotalSegments(),
                                        segmentMessageGroup.getType());

        } catch (Exception e) {
            log.error("重组分段消息异常 - key: {}", segmentMessageGroup.getCacheKey(), e);
            return null;
        }
    }

    /**
     * 解码单个分段的消息内容（使用统一的服务端编码）
     *
     * @param segment 当前分段
     * @param serverEncoding 服务端编码配置
     * @param segmentIndex 分段序号（用于日志）
     * @return 解码后的文本内容
     */
    private String decodeSegmentMessage(SubmitSm segment, Integer serverEncoding, int segmentIndex) {
        byte[] messageBytes = segment.getShortMessage();
        if (messageBytes == null || messageBytes.length == 0) {
            log.debug("分段 {} 消息字节为空", segmentIndex);
            return "";
        }

        try {
            log.info("解码分段 {} - 编码: {}, 字节长度: {}, 字节内容: {}",
                    segmentIndex, serverEncoding, messageBytes.length, bytesToHexString(messageBytes));

            String decodedContent = encodingConverter.decode(messageBytes, serverEncoding);

            log.info("分段 {} 解码成功 - 内容: '{}'", segmentIndex, decodedContent);
            return decodedContent;
        } catch (Exception e) {
            log.error("解码分段 {} 异常 - 编码: {}, 字节长度: {}",
                    segmentIndex, serverEncoding, messageBytes.length, e);
            // 降级处理：使用GSM编码
            try {
                String fallbackContent = encodingConverter.decode(messageBytes, 1);
                log.warn("分段 {} 降级使用GSM编码解码结果: '{}'", segmentIndex, fallbackContent);
                return fallbackContent;
            } catch (Exception fallbackException) {
                log.error("分段 {} 降级解码也失败", segmentIndex, fallbackException);
                return "";
            }
        }
    }

    /**
     * 字节数组转十六进制字符串（用于日志）
     */
    private String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < Math.min(bytes.length, 50); i++) { // 最多显示50个字节
            sb.append(String.format("%02X ", bytes[i]));
        }
        if (bytes.length > 50) {
            sb.append("...(").append(bytes.length).append(" bytes total)");
        }
        return sb.toString().trim();
    }

    /**
     * 验证分段组的完整性
     * 
     * @param segmentMessageGroup 分段组
     * @return 验证结果
     */
    public boolean validateSegmentGroup(SegmentMessageGroup segmentMessageGroup) {
        if (segmentMessageGroup == null) {
            return false;
        }

        // 检查是否完整
        if (!segmentMessageGroup.isComplete()) {
            return false;
        }

        // 检查所有分段是否存在
        for (int i = 1; i <= segmentMessageGroup.getTotalSegments(); i++) {
            if (!segmentMessageGroup.hasSegment(i)) {
                log.error("分段组验证失败：缺失分段 {} - key: {}", i, segmentMessageGroup.getCacheKey());
                return false;
            }
        }

        return true;
    }
}
