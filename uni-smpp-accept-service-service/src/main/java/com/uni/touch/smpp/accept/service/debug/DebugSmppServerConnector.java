package com.uni.touch.smpp.accept.service.debug;

import com.cloudhopper.smpp.channel.SmppChannelConstants;
import com.cloudhopper.smpp.channel.SmppSessionPduDecoder;
import com.cloudhopper.smpp.channel.SmppSessionThreadRenamer;
import com.cloudhopper.smpp.channel.SmppSessionWrapper;
import com.cloudhopper.smpp.impl.DefaultSmppServer;
import com.cloudhopper.smpp.impl.UnboundSmppSession;
import com.cloudhopper.smpp.ssl.SslConfiguration;
import com.cloudhopper.smpp.ssl.SslContextFactory;
import lombok.extern.slf4j.Slf4j;
import org.jboss.netty.channel.Channel;
import org.jboss.netty.channel.ChannelHandlerContext;
import org.jboss.netty.channel.ChannelPipelineCoverage;
import org.jboss.netty.channel.ChannelStateEvent;
import org.jboss.netty.channel.SimpleChannelUpstreamHandler;
import org.jboss.netty.channel.group.ChannelGroup;
import org.jboss.netty.handler.ssl.SslHandler;

import javax.net.ssl.SSLEngine;

/**
 * 调试版本的SMPP服务器连接器
 * 在原有功能基础上添加详细的调试日志
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Slf4j
@ChannelPipelineCoverage("all")
public class DebugSmppServerConnector extends SimpleChannelUpstreamHandler {

    private ChannelGroup channels;
    private DefaultSmppServer server;

    public DebugSmppServerConnector(ChannelGroup channels, DefaultSmppServer server) {
        this.channels = channels;
        this.server = server;
    }

    @Override
    public void channelConnected(ChannelHandlerContext ctx, ChannelStateEvent e) throws Exception {
        Channel channel = e.getChannel();
        
        log.info("=== 新的SMPP客户端连接 ===");
        log.info("客户端地址: {}", channel.getRemoteAddress());
        log.info("服务器地址: {}", channel.getLocalAddress());
        log.info("Channel ID: {}", channel.getId());

        // 添加到channel组
        channels.add(channel);
        this.server.getCounters().incrementChannelConnectsAndGet();

        // 创建channel名称
        String channelName = createChannelName(channel);
        String threadName = server.getConfiguration().getName() + ".UnboundSession." + channelName;
        
        log.info("Channel名称: {}", channelName);
        log.info("线程名称: {}", threadName);

        try {
            // 添加SSL处理器（如果启用）
            if (server.getConfiguration().isUseSsl()) {
                log.info("启用SSL支持");
                SslConfiguration sslConfig = server.getConfiguration().getSslConfiguration();
                if (sslConfig == null) {
                    throw new IllegalStateException("sslConfiguration must be set");
                }
                SslContextFactory factory = new SslContextFactory(sslConfig);
                SSLEngine sslEngine = factory.newSslEngine();
                sslEngine.setUseClientMode(false);
                channel.getPipeline().addLast(SmppChannelConstants.PIPELINE_SESSION_SSL_NAME, new SslHandler(sslEngine));
            }

            // 添加调试处理器 - 在最前面
            channel.getPipeline().addLast("debug-raw", new SmppDebugHandler("RAW"));

            // 添加线程重命名器
            log.info("添加线程重命名器");
            channel.getPipeline().addLast(SmppChannelConstants.PIPELINE_SESSION_THREAD_RENAMER_NAME, 
                    new SmppSessionThreadRenamer(threadName));

            // 添加调试处理器 - 在解码器之前
            channel.getPipeline().addLast("debug-before-decoder", new SmppDebugHandler("BEFORE_DECODER"));

            // 添加PDU解码器
            log.info("添加PDU解码器");
            channel.getPipeline().addLast(SmppChannelConstants.PIPELINE_SESSION_PDU_DECODER_NAME, 
                    new SmppSessionPduDecoder(server.getTranscoder()));

            // 添加调试处理器 - 在解码器之后
            channel.getPipeline().addLast("debug-after-decoder", new SmppDebugHandler("AFTER_DECODER"));

            // 创建UnboundSmppSession
            log.info("创建UnboundSmppSession");
            UnboundSmppSession session = new UnboundSmppSession(channelName, channel, server);
            
            // 添加会话包装器
            log.info("添加会话包装器");
            channel.getPipeline().addLast(SmppChannelConstants.PIPELINE_SESSION_WRAPPER_NAME, 
                    new SmppSessionWrapper(session));

            log.info("=== Pipeline配置完成 ===");
            log.info("Pipeline处理器列表:");
            channel.getPipeline().getNames().forEach(name -> 
                log.info("  - {}: {}", name, channel.getPipeline().get(name).getClass().getSimpleName()));

        } catch (Exception ex) {
            log.error("配置Pipeline时发生异常", ex);
            channel.close();
            throw ex;
        }
    }

    @Override
    public void channelDisconnected(ChannelHandlerContext ctx, ChannelStateEvent e) throws Exception {
        log.info("客户端连接断开: {}", e.getChannel().getRemoteAddress());
        channels.remove(e.getChannel());
        this.server.getCounters().incrementChannelDisconnectsAndGet();
    }

    /**
     * 创建Channel名称
     */
    private String createChannelName(Channel channel) {
        StringBuilder sb = new StringBuilder();
        if (channel.getRemoteAddress() != null) {
            sb.append(channel.getRemoteAddress().toString());
        } else {
            sb.append("unknown");
        }
        return sb.toString();
    }
}
