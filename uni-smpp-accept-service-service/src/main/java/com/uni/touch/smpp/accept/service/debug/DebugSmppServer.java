package com.uni.touch.smpp.accept.service.debug;

import com.cloudhopper.smpp.SmppServerConfiguration;
import com.cloudhopper.smpp.SmppServerHandler;
import com.cloudhopper.smpp.channel.SmppChannelConstants;
import com.cloudhopper.smpp.impl.DefaultSmppServer;
import com.cloudhopper.smpp.transcoder.DefaultPduTranscoder;
import com.cloudhopper.smpp.transcoder.DefaultPduTranscoderContext;
import lombok.extern.slf4j.Slf4j;
import org.jboss.netty.bootstrap.ServerBootstrap;
import org.jboss.netty.channel.ChannelException;
import org.jboss.netty.channel.group.ChannelGroup;
import org.jboss.netty.channel.group.DefaultChannelGroup;
import org.jboss.netty.channel.socket.nio.NioServerSocketChannelFactory;
import org.jboss.netty.channel.socket.oio.OioServerSocketChannelFactory;

import java.net.InetSocketAddress;
import java.util.Timer;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;

/**
 * 调试版本的SMPP服务器
 * 继承DefaultSmppServer并添加详细的调试功能
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Slf4j
public class DebugSmppServer extends DefaultSmppServer {

    public DebugSmppServer(SmppServerConfiguration configuration, 
                          SmppServerHandler serverHandler, 
                          ExecutorService executor, 
                          ScheduledExecutorService monitorExecutor) {
        super(configuration, serverHandler, executor, monitorExecutor);
        log.info("创建DebugSmppServer实例");
    }

    @Override
    public void start() throws com.cloudhopper.smpp.type.SmppChannelException {
        log.info("=== 开始启动调试版SMPP服务器 ===");
        log.info("配置信息:");
        log.info("  - 主机: {}", getConfiguration().getHost());
        log.info("  - 端口: {}", getConfiguration().getPort());
        log.info("  - 最大连接数: {}", getConfiguration().getMaxConnectionSize());
        log.info("  - 绑定超时: {}ms", getConfiguration().getBindTimeout());
        log.info("  - 非阻塞Socket: {}", getConfiguration().isNonBlockingSocketsEnabled());
        log.info("  - 重用地址: {}", getConfiguration().isReuseAddress());
        
        try {
            // 调用父类的启动方法，但添加更多日志
            super.start();
            log.info("=== SMPP服务器启动成功 ===");
            
        } catch (Exception e) {
            log.error("=== SMPP服务器启动失败 ===", e);
            throw e;
        }
    }

    // 由于无法访问protected方法，我们通过反射或其他方式来实现调试功能
    // 这里先简化实现，重点是添加日志
}
