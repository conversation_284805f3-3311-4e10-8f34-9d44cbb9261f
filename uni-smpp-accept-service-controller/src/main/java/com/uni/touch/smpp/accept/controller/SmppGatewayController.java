package com.uni.touch.smpp.accept.controller;

import com.uni.touch.smpp.accept.service.server.SmppServerStarter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/07/30
 */
@RestController
@RequestMapping("/smpp/gateway")
public class SmppGatewayController {

    @Autowired
    private SmppServerStarter smppServerStarter;

    /**
     * 获取网关状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();

        status.put("smpp_gateway_running", smppServerStarter.getStatus());

        return ResponseEntity.ok(status);
    }
}
